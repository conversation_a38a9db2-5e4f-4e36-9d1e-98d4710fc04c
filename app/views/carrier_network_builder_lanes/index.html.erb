<% content_for :title, @carrier_network_builder.name %>
<%= stylesheet_link_tag 'background-gradient', media: 'all' %>

<div class="-z-10 h-screen lite-gradient-background">

  <div class="contained-width">
    <div class="flex items-center py-4">
      <%= image_tag 'carrier_source_logo.webp', size: '151x40', class: 'inline-block', alt: 'CarrierSource Logo' %>
      <div class="flex flex-auto justify-end font-semibold text-black">Carrier Network Builder</div>
    </div>

    <div class="flex gap-6  h-[calc(100vh-72px-36px)]">

      <div class="flex h-full flex-col rounded-xl bg-white shadow-xl w-[36%]"
           data-controller="ai-chatbot"
           data-ai-chatbot-endpoint-value="/ai_chatbot"
           data-ai-chatbot-carrier-network-builder-id-value="<%= @carrier_network_builder.id %>">
        <div class="flex flex-auto flex-col gap-4 overflow-y-auto p-4" data-ai-chatbot-target="messages">
          <div class="flex gap-3">
            <div class="flex h-8 w-8 flex-none items-center justify-center rounded-full fancy-border-outline">
              <%= svg_tag 'logo-gradient-small', class: 'h-7 w-7 fill-primary -ml-px' %>
            </div>
            <div class="mr-12 rounded-lg to-purple-100 p-3 bg-linear-90 from-primary-100 space-y-2">
              <p>Hi, I am your shipping assistant. I can help you build carrier networks, find the right carriers and brokerages for
                your lanes, and more!</p>
              <p>Tell me about your shipping needs: origin, destination, volume, special requirements, etc.</p>
            </div>
          </div>
        </div>

        <!-- Left Column Footer-->
        <div class="flex flex-none items-center gap-2 border-t border-t-gray-300 px-4 h-18">
          <div class="cursor-pointer rounded-full p-3 hover:bg-primary-100">
            <%= svg_tag 'upload-regular', class: 'h-4 w-4 fill-gray-600' %>
          </div>
          <div class="flex-auto">
            <input class="w-full rounded-full fancy-border-outline" placeholder="Type your Message" />
          </div>
        </div>
      </div>

      <div class="flex flex-auto flex-col rounded-xl bg-secondary fancy-border-outline">
        <div class="flex items-center border-b border-gray-300 p-4">
          <h2 class="flex-auto text-lg font-semibold text-black"><%= @carrier_network_builder.name %></h2>
          <div class="flex h-9 flex-none cursor-pointer gap-2 btn default gradient">
            <%= svg_tag 'file-arrow-down-regular', class: 'h-4 w-4 fill-white' %><span>Export</span></div>
        </div>

        <div class="flex-auto overflow-y-auto transparent-sidebar">
          <% if @lanes.any? %>
            <div id="lanes-body">
              <% @lanes.each do |lane| %>
                <%= render 'carrier_network_builder_lanes/lane',
                           lane:, carrier_network_builder: @carrier_network_builder %>
              <% end %>
            </div>
          <% else %>
            <div class="py-12 text-center">
              <div class="mx-auto h-12 w-12 text-gray-400">
                <%= svg_tag 'road-regular', class: 'h-12 w-12' %>
              </div>
              <h3 class="mt-2 text-sm font-medium text-gray-900">No lanes</h3>
              <p class="mt-1 text-sm text-gray-500">Get started by adding a lane to this network builder.</p>
              <div class="mt-6">
                <%= link_to 'Add Lane', new_carrier_network_builder_lane_path(@carrier_network_builder),
                            class: 'btn default primary' %>
              </div>
            </div>
          <% end %>
          <div class="flex justify-end p-4">
            <%= link_to 'Add Lane', new_carrier_network_builder_lane_path(@carrier_network_builder),
                        data: { turbo_frame: 'modal' }, class: 'btn hollow gradient py-1' %>
          </div>
        </div>
      </div>

    </div>
  </div>
</div>
