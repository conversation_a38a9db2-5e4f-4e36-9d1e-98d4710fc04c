module Forms
  class ShipperRequirements < Forms::Base
    # Required fields
    attribute :origin_type, Types::String.optional.enum('city', 'state', 'region')
    attribute :origin_value, Types::String.optional
    attribute :destination_type, Types::String.optional.enum('city', 'state', 'region')
    attribute :destination_value, Types::String.optional

    # Optional picklist fields
    attribute :truck_type, Types::Registry['filters.truck_types']
    attribute :shipment_type, Types::Registry['filters.shipment_types']
    attribute :specialized_service, Types::Registry['filters.specialized_services']
    attribute :freight, Types::Registry['filters.freights']

    # Optional free-form fields
    attribute :volume, Types::String.optional
    attribute :frequency, Types::String.optional

    def valid?
      origin_complete? && destination_complete?
    end

    def to_search_filters
      {
        origin: location_filter(:origin),
        destination: location_filter(:destination),
        truck_type_ids: truck_type,
        shipment_type_ids: shipment_type,
        specialized_service_ids: specialized_service,
        freight_ids: freight,
        volume: volume,
        frequency: frequency
      }.compact_blank
    end

    def to_es_query
      filters = to_search_filters.except(:volume, :frequency)
      { filters: filters }
    end

    private

    def origin_complete?
      origin_type.present? && origin_value.present?
    end

    def destination_complete?
      destination_type.present? && destination_value.present?
    end

    def location_filter(prefix)
      type = public_send("#{prefix}_type")
      value = public_send("#{prefix}_value")
      
      return nil unless type.present? && value.present?
      
      { type: type, value: value }
    end
  end
end
