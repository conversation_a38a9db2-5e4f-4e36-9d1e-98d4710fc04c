module AI
  class ShipperProspectingChatbot
    include Callable

    SYSTEM_PROMPT = <<~PROMPT.freeze
      You are an AI assistant helping shippers find carriers for their shipping lanes. Your goal is to collect shipping requirements to search for carriers.

      REQUIRED INFORMATION TO COLLECT:
      1. Origin (city, state, or region in the US)
      2. Destination (city, state, or region in the US)

      OPTIONAL INFORMATION TO COLLECT:
      3. Truck Type: #{Records[:truck_types].all.map(&:name).join(', ')}
      4. Shipment Type: #{Records[:shipment_types].all.map(&:name).join(', ')}
      5. Specialized Services: #{Records[:specialized_services].all.map(&:name).join(', ')}
      6. Freights: #{Records[:freights].all.map(&:name).join(', ')}
      7. Volume (free-form text)
      8. Frequency (free-form text)

      CONVERSATION RULES:
      - Be friendly and professional
      - Ask for one piece of information at a time
      - Always collect origin and destination first
      - For locations, accept city/state combinations, state names, or regions
      - For picklist items, suggest options if user is unsure
      - Once you have origin and destination, ask if they want to specify optional requirements
      - When you have enough information, respond with ONLY a JSON object in this format:
      {
        "origin_type": "city|state|region",
        "origin_value": "location name",
        "destination_type": "city|state|region", 
        "destination_value": "location name",
        "truck_type": ["truck_type_name"],
        "shipment_type": ["shipment_type_name"],
        "specialized_service": ["service_name"],
        "freight": ["freight_name"],
        "volume": "volume description",
        "frequency": "frequency description"
      }

      Start by greeting the user and asking about their shipping origin.
    PROMPT

    attr_reader :messages, :client

    def initialize(messages = [])
      @messages = messages
      @client = OpenAI::Api::ChatCompletion.new(raise_errors: true)
    end

    def call
      response = client.create(
        messages: conversation_messages,
        model: 'gpt-4o-mini',
        temperature: 0.7,
        max_tokens: 1000
      )

      content = response.parse.dig('choices', 0, 'message', 'content')
      
      # Check if response is JSON (final result)
      if json_response?(content)
        parse_final_response(content)
      else
        { type: 'message', content: content }
      end
    end

    private

    def conversation_messages
      [
        { role: 'system', content: SYSTEM_PROMPT },
        *messages
      ]
    end

    def json_response?(content)
      content.strip.start_with?('{') && content.strip.end_with?('}')
    end

    def parse_final_response(content)
      requirements = JSON.parse(content)
      form = Forms::ShipperRequirements.new(requirements)
      
      if form.valid?
        {
          type: 'requirements',
          data: form.to_search_filters,
          elasticsearch_query: form.to_es_query
        }
      else
        {
          type: 'error',
          content: 'I need both origin and destination to search for carriers. Let me ask you again.'
        }
      end
    rescue JSON::ParserError
      { type: 'message', content: content }
    end
  end
end
