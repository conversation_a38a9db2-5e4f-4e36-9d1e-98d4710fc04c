module CarrierNetworkBuilders
  class ShipperProspectingChatbot
    include Callable

    SYSTEM_PROMPT = <<~PROMPT.freeze
      You are an AI assistant helping shippers find carriers and brokerages for their shipping lanes. Your goal is to collect shipping requirements to search for carriers and brokerages.

      REQUIRED INFORMATION TO COLLECT:
      1. Origin (city, state, or region in the US)
      2. Destination (city, state, or region in the US)

      OPTIONAL INFORMATION TO COLLECT:
      3. Truck Type: #{Records[:truck_types].all.map(&:slug).join(', ')}
      4. Shipment Type: #{Records[:shipment_types].all.map(&:slug).join(', ')}
      5. Specialized Services: #{Records[:specialized_services].all.map(&:slug).join(', ')}
      6. Freights: #{Records[:freights].all.map(&:slug).join(', ')}
      7. Volume (free-form text)
      8. Frequency (free-form text)

      CONVERSATION RULES:
      - Be friendly and professional
      - Ask for one piece of information at a time
      - Always collect origin and destination first
      - For locations, accept city/state combinations, state names, or regions
      - For picklist items, suggest options if user is unsure
      - SMART INFERENCE: When users mention freight types, automatically infer the appropriate truck type, shipment type, and specialized services using SLUGS:
        * Refrigerated Food, Meat, frozen/cold items → reefer truck type, refrigerated-food freight
        * Motor Vehicles, cars, vehicles → auto_carrier truck type, motor-vehicles freight
        * Liquids/Gases, fuel, chemicals → tanker truck type, liquids-gases freight, hazardous-materials specialized service if hazmat
        * Machinery, Large Objects, construction equipment → flatbed truck type, machinery-large-objects freight
        * Intermodal Containers → container truck type, intermodal-containers freight
        * Building Materials, lumber, steel → flatbed truck type, building-materials freight
        * General Freight, packaged goods → van truck type, general-freight freight
        * Fresh Produce → reefer truck type, fresh-produce freight
        * Chemicals → van truck type, chemicals freight, hazardous-materials specialized service
        * Small shipments, partial loads, LTL → less-than-truckload shipment type
        * Full trailer loads, FTL → full-truckload shipment type
        * Time-sensitive, urgent, expedited → team-drivers specialized service
        * Cross-border, Mexico/Canada → cross-border specialized service
      - Once you have origin and destination, ask if they want to specify optional requirements
      - When you have enough information, respond with ONLY a JSON object in this format:
      {
        "origin": "hierarchical_location_format",
        "destination": "hierarchical_location_format",
        "truck_type": ["truck_type_slug"],
        "shipment_type": ["shipment_type_slug"],
        "specialized_service": ["service_slug"],
        "freight": ["freight_slug"],
        "volume": "volume description",
        "frequency": "frequency description"
      }

      IMPORTANT: Use actual slugs (like reefer, van, flatbed, ftl, ltl) NOT names in quotes. The slugs should match the database slug values exactly.

      LOCATION FORMAT RULES:
      - Cities: "country:state:city" (e.g., "united-states:colorado:denver", "canada:ontario:toronto", "mexico:nuevo-leon:monterrey")
      - States: "country:state" (e.g., "united-states:utah", "canada:alberta", "mexico:sonora")
      - US Regions: "united-states:region" (e.g., "united-states:midwest", "united-states:northeast", "united-states:southeast", "united-states:southwest", "united-states:west")
      - Canada and Mexico do NOT have regions, only cities and states
      - Use lowercase and hyphens for all location parts (e.g., "new-york" not "New York")

      EXAMPLES OF SMART INFERENCE WITH ACTUAL SLUGS:
      - User: "I need to ship frozen food from Chicago to Miami" → Return: {"origin": "united-states:illinois:chicago", "destination": "united-states:florida:miami", "truck_type": ["reefer"], "freight": ["coldfood"]}
      - User: "Moving cars from Detroit to Los Angeles" → Return: {"origin": "united-states:michigan:detroit", "destination": "united-states:california:los-angeles", "truck_type": ["auto_carrier"], "freight": ["motoveh"]}
      - User: "Shipping from Texas to the West region" → Return: {"origin": "united-states:texas", "destination": "united-states:west"}
      - User: "Need to move equipment from Toronto to Denver" → Return: {"origin": "canada:ontario:toronto", "destination": "united-states:colorado:denver", "truck_type": ["flatbed"], "freight": ["machlrg"]}
      - User: "Small LTL shipment from California" → Return: {"origin": "united-states:california", "shipment_type": ["ltl"]}
      - User: "Full truckload of chemicals" → Return: {"truck_type": ["van"], "freight": ["chem"], "shipment_type": ["ftl"], "specialized_service": ["hazardous-materials"]}

      Start by greeting the user and asking about their shipping origin. Help them find both carriers and brokerages that can handle their shipping needs. Use smart inference to reduce questions when freight type gives clear indicators.
    PROMPT

    attr_reader :messages, :client

    def initialize(messages = [])
      @messages = messages
      @client = OpenAI::Api::ChatCompletion.new(raise_errors: true)
    end

    def call
      response = client.create(
        messages: conversation_messages,
        model: 'gpt-4o-mini',
        temperature: 0.7,
        max_tokens: 1000
      )

      content = response.parse.dig('choices', 0, 'message', 'content')

      # Check if response is JSON (final result)
      if json_response?(content)
        parse_final_response(content)
      else
        { type: 'message', content: content }
      end
    end

    private

    def conversation_messages
      [
        { role: 'system', content: SYSTEM_PROMPT },
        *messages
      ]
    end

    def json_response?(content)
      content.strip.start_with?('{') && content.strip.end_with?('}')
    end

    def parse_final_response(content)
      requirements = JSON.parse(content)

      # Look up origin and destination using hierarchical format
      origin_location = parse_hierarchical_location(requirements['origin'])
      destination_location = parse_hierarchical_location(requirements['destination'])

      if origin_location.nil? || destination_location.nil?
        return {
          type: 'error',
          content: 'I couldn\'t find the locations you mentioned. Please provide valid location names.'
        }
      end

      # Build lane search form with the found locations and optional filters
      form_params = {
        origin: origin_location,
        destination: destination_location
      }

      # Add optional filters if provided
      form_params[:truck_type] = lookup_truck_types(requirements['truck_type']) if requirements['truck_type'].present?
      form_params[:shipment_type] = lookup_shipment_types(requirements['shipment_type']) if requirements['shipment_type'].present?
      form_params[:specialized_service] = lookup_specialized_services(requirements['specialized_service']) if requirements['specialized_service'].present?
      form_params[:freight] = lookup_freights(requirements['freight']) if requirements['freight'].present?

      form = Forms::LaneSearch.new(form_params)

      {
        type: 'requirements',
        data: {
          origin: origin_location.respond_to?(:label) ? origin_location.label : origin_location.name,
          destination: destination_location.respond_to?(:label) ? destination_location.label : destination_location.name,
          volume: requirements['volume'],
          frequency: requirements['frequency'],
          **form_params.except(:origin, :destination)
        },
        elasticsearch_query: form.to_es_query
      }
    rescue JSON::ParserError
      { type: 'message', content: content }
    end

    def lookup_truck_types(slugs)
      return [] if slugs.blank?
      Array.wrap(slugs).filter_map { |slug| find_record_by_slug(Records[:truck_types].all, slug) }
    end

    def lookup_shipment_types(slugs)
      return [] if slugs.blank?
      Array.wrap(slugs).filter_map { |slug| find_record_by_slug(Records[:shipment_types].all, slug) }
    end

    def lookup_specialized_services(slugs)
      return [] if slugs.blank?
      Array.wrap(slugs).filter_map { |slug| find_record_by_slug(Records[:specialized_services].all, slug) }
    end

    def lookup_freights(slugs)
      return [] if slugs.blank?
      Array.wrap(slugs).filter_map { |slug| find_record_by_slug(Records[:freights].all, slug) }
    end

    def find_record_by_slug(records, slug)
      slug_normalized = slug.downcase.strip

      # Try exact slug match first
      record = records.find { |r| r.slug == slug_normalized }
      return record.id if record

      # Try with underscores converted to hyphens
      slug_with_hyphens = slug_normalized.gsub('_', '-')
      record = records.find { |r| r.slug == slug_with_hyphens }
      return record.id if record

      # Try with hyphens converted to underscores
      slug_with_underscores = slug_normalized.gsub('-', '_')
      record = records.find { |r| r.slug == slug_with_underscores }
      return record.id if record

      # For truck types, also try key-based matching
      if records.first.respond_to?(:key)
        record = records.find { |r| r.key == slug_with_underscores }
        return record.id if record
      end

      nil
    end

    def find_record_by_name(records, name)
      name_lower = name.downcase.strip

      # Try exact match first
      record = records.find { |r| r.name.downcase == name_lower }
      return record.id if record

      # Try partial match for common variations
      record = records.find { |r| r.name.downcase.include?(name_lower) || name_lower.include?(r.name.downcase) }
      return record.id if record

      # Try key-based matching for truck types
      if records.first.respond_to?(:key)
        record = records.find { |r| r.key.downcase == name_lower.gsub(/\s+/, '_') }
        return record.id if record
      end

      nil
    end

    def parse_hierarchical_location(location_string)
      return nil if location_string.blank?

      parts = location_string.split(':')
      return nil if parts.length < 2

      country = parts[0]

      case parts.length
      when 2
        # Format: "country:state" or "country:region"
        location_name = parts[1]

        if country == 'united-states'
          # Check if it's a region first
          us_country = Geo::Country.find('us')
          if us_country
            region = us_country.regions.find { |r| r.name.downcase.gsub(/\s+/, '-') == location_name }
            return region if region
          end
        end

        # Otherwise, treat as state
        find_state_by_slug_or_name(country, location_name)

      when 3
        # Format: "country:state:city"
        state_name = parts[1]
        city_name = parts[2]

        find_city_by_hierarchical_path(country, state_name, city_name)

      else
        nil
      end
    end

    def find_state_by_slug_or_name(country, location_name)
      country_code = country == 'united-states' ? 'us' : country
      geo_country = Geo::Country.find(country_code)
      return nil unless geo_country

      # Try by slug first
      state = geo_country.states.find { |s| s.slug == location_name }
      return state if state

      # Try by name (case insensitive, with hyphens converted to spaces)
      name_to_match = location_name.gsub('-', ' ')
      geo_country.states.find { |s| s.name.downcase == name_to_match.downcase }
    end

    def find_city_by_hierarchical_path(country, state_name, city_name)
      # Find the state first
      state = find_state_by_slug_or_name(country, state_name)
      return nil unless state

      # Convert city name from slug format to searchable format
      city_search_name = city_name.gsub('-', ' ')

      # Search for city in that state
      City.where(state_code: state.abbr)
          .where('LOWER(name) = ?', city_search_name.downcase)
          .first
    end
  end
end
