module CarrierNetworkBuilders
  class ShipperProspectingChatbot
    include Callable

    SYSTEM_PROMPT = <<~PROMPT.freeze
      You are an AI assistant helping shippers find carriers for their shipping lanes. Your goal is to collect shipping requirements to search for carriers.

      REQUIRED INFORMATION TO COLLECT:
      1. Origin (city, state, or region in the US)
      2. Destination (city, state, or region in the US)

      OPTIONAL INFORMATION TO COLLECT:
      3. Truck Type: #{Records[:truck_types].all.map(&:name).join(', ')}
      4. Shipment Type: #{Records[:shipment_types].all.map(&:name).join(', ')}
      5. Specialized Services: #{Records[:specialized_services].all.map(&:name).join(', ')}
      6. Freights: #{Records[:freights].all.map(&:name).join(', ')}
      7. Volume (free-form text)
      8. Frequency (free-form text)

      CONVERSATION RULES:
      - Be friendly and professional
      - Ask for one piece of information at a time
      - Always collect origin and destination first
      - For locations, accept city/state combinations, state names, or regions
      - For picklist items, suggest options if user is unsure
      - Once you have origin and destination, ask if they want to specify optional requirements
      - When you have enough information, respond with ONLY a JSON object in this format:
      {
        "origin": "location name",
        "destination": "location name",
        "truck_type": ["truck_type_name"],
        "shipment_type": ["shipment_type_name"],
        "specialized_service": ["service_name"],
        "freight": ["freight_name"],
        "volume": "volume description",
        "frequency": "frequency description"
      }

      Start by greeting the user and asking about their shipping origin.
    PROMPT

    attr_reader :messages, :client

    def initialize(messages = [])
      @messages = messages
      @client = OpenAI::Api::ChatCompletion.new(raise_errors: true)
    end

    def call
      response = client.create(
        messages: conversation_messages,
        model: 'gpt-4o-mini',
        temperature: 0.7,
        max_tokens: 1000
      )

      content = response.parse.dig('choices', 0, 'message', 'content')
      
      # Check if response is JSON (final result)
      if json_response?(content)
        parse_final_response(content)
      else
        { type: 'message', content: content }
      end
    end

    private

    def conversation_messages
      [
        { role: 'system', content: SYSTEM_PROMPT },
        *messages
      ]
    end

    def json_response?(content)
      content.strip.start_with?('{') && content.strip.end_with?('}')
    end

    def parse_final_response(content)
      requirements = JSON.parse(content)

      # Look up origin and destination cities
      origin_city = CarrierNetworkBuilders::LocationLookup.call(requirements['origin'])
      destination_city = CarrierNetworkBuilders::LocationLookup.call(requirements['destination'])

      if origin_city.nil? || destination_city.nil?
        return {
          type: 'error',
          content: 'I couldn\'t find the cities you mentioned. Please provide valid city names with state (e.g., "Chicago, IL") or state names.'
        }
      end

      # Build lane search form with the found cities and optional filters
      form_params = {
        origin: origin_city,
        destination: destination_city
      }

      # Add optional filters if provided
      form_params[:truck_type] = lookup_truck_types(requirements['truck_type']) if requirements['truck_type'].present?
      form_params[:shipment_type] = lookup_shipment_types(requirements['shipment_type']) if requirements['shipment_type'].present?
      form_params[:specialized_service] = lookup_specialized_services(requirements['specialized_service']) if requirements['specialized_service'].present?
      form_params[:freight] = lookup_freights(requirements['freight']) if requirements['freight'].present?

      form = Forms::LaneSearch.new(form_params)

      {
        type: 'requirements',
        data: {
          origin: origin_city.label,
          destination: destination_city.label,
          volume: requirements['volume'],
          frequency: requirements['frequency'],
          **form_params.except(:origin, :destination)
        },
        elasticsearch_query: form.to_es_query
      }
    rescue JSON::ParserError
      { type: 'message', content: content }
    end

    def lookup_truck_types(names)
      return [] if names.blank?
      Array.wrap(names).filter_map { |name| Records[:truck_types].all.find { |tt| tt.name.downcase == name.downcase }&.id }
    end

    def lookup_shipment_types(names)
      return [] if names.blank?
      Array.wrap(names).filter_map { |name| Records[:shipment_types].all.find { |st| st.name.downcase == name.downcase }&.id }
    end

    def lookup_specialized_services(names)
      return [] if names.blank?
      Array.wrap(names).filter_map { |name| Records[:specialized_services].all.find { |ss| ss.name.downcase == name.downcase }&.id }
    end

    def lookup_freights(names)
      return [] if names.blank?
      Array.wrap(names).filter_map { |name| Records[:freights].all.find { |f| f.name.downcase == name.downcase }&.id }
    end
  end
end
