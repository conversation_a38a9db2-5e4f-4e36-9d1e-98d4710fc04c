module CarrierNetworkBuilders
  class ShipperProspectingChatbot
    include Callable

    SYSTEM_PROMPT = <<~PROMPT.freeze
      You are an AI assistant helping shippers find carriers and brokerages for their shipping lanes. Your goal is to collect shipping requirements to search for carriers and brokerages.

      REQUIRED INFORMATION TO COLLECT:
      1. Origin (city, state, or region in the US)
      2. Destination (city, state, or region in the US)

      OPTIONAL INFORMATION TO COLLECT:
      3. Truck Type: #{Records[:truck_types].all.map(&:name).join(', ')}
      4. Shipment Type: #{Records[:shipment_types].all.map(&:name).join(', ')}
      5. Specialized Services: #{Records[:specialized_services].all.map(&:name).join(', ')}
      6. Freights: #{Records[:freights].all.map(&:name).join(', ')}
      7. Volume (free-form text)
      8. Frequency (free-form text)

      CONVERSATION RULES:
      - Be friendly and professional
      - Ask for one piece of information at a time
      - Always collect origin and destination first
      - For locations, accept city/state combinations, state names, or regions
      - For picklist items, suggest options if user is unsure
      - SMART INFERENCE: When users mention freight types, automaticaIlly infer the appropriate truck type, shipment type, and specialized services:
        * Refrigerated Food, Meat, frozen/cold items → "Reefer" truck type
        * Motor Vehicles, cars, vehicles → "Auto Carrier" truck type
        * Liquids/Gases, fuel, chemicals → "Tanker" truck type, "Hazardous Materials" specialized service if hazmat
        * Machinery, Large Objects, construction equipment → "Flatbed" truck type
        * Intermodal Containers → "Container" truck type
        * Building Materials, lumber, steel → "Flatbed" truck type
        * General Freight, packaged goods → "Dry Van" truck type
        * Fresh Produce → "Reefer" truck type
        * Chemicals → "Dry Van" truck type, "Hazardous Materials" specialized service
        * Small shipments, partial loads, LTL → "Less than Truckload" shipment type
        * Full trailer loads, FTL → "Full Truckload" shipment type
        * Time-sensitive, urgent, expedited → "Team Drivers" specialized service
        * Cross-border, Mexico/Canada → "Cross Border" specialized service
      - Once you have origin and destination, ask if they want to specify optional requirements
      - When you have enough information, respond with ONLY a JSON object in this format:
      {
        "origin": "location name",
        "destination": "location name",
        "truck_type": ["truck_type_name"],
        "shipment_type": ["shipment_type_name"],
        "specialized_service": ["service_name"],
        "freight": ["freight_name"],
        "volume": "volume description",
        "frequency": "frequency description"
      }

      EXAMPLES OF SMART INFERENCE:
      - User: "I need to ship frozen food from Chicago to Miami" → Infer: "Reefer" truck type, "Refrigerated Food" freight
      - User: "Moving cars from Detroit to Los Angeles" → Infer: "Auto Carrier" truck type, "Motor Vehicles" freight
      - User: "Shipping chemicals from Houston to Denver" → Infer: "Dry Van" truck type, "Chemicals" freight, "Hazardous Materials" service
      - User: "Need to move construction equipment urgently" → Infer: "Flatbed" truck type, "Machinery, Large Objects" freight, "Team Drivers" service
      - User: "Small shipment of electronics, only 5 pallets" → Infer: "Dry Van" truck type, "Less than Truckload" shipment type
      - User: "Full truckload of produce" → Infer: "Reefer" truck type, "Fresh Produce" freight, "Full Truckload" shipment type

      Start by greeting the user and asking about their shipping origin. Help them find both carriers and brokerages that can handle their shipping needs. Use smart inference to reduce questions when freight type gives clear indicators.
    PROMPT

    attr_reader :messages, :client

    def initialize(messages = [])
      @messages = messages
      @client = OpenAI::Api::ChatCompletion.new(raise_errors: true)
    end

    def call
      response = client.create(
        messages: conversation_messages,
        model: 'gpt-4o-mini',
        temperature: 0.7,
        max_tokens: 1000
      )

      content = response.parse.dig('choices', 0, 'message', 'content')

      # Check if response is JSON (final result)
      if json_response?(content)
        parse_final_response(content)
      else
        { type: 'message', content: content }
      end
    end

    private

    def conversation_messages
      [
        { role: 'system', content: SYSTEM_PROMPT },
        *messages
      ]
    end

    def json_response?(content)
      content.strip.start_with?('{') && content.strip.end_with?('}')
    end

    def parse_final_response(content)
      requirements = JSON.parse(content)

      # Look up origin and destination cities
      origin_city = CarrierNetworkBuilders::LocationLookup.call(requirements['origin'])
      destination_city = CarrierNetworkBuilders::LocationLookup.call(requirements['destination'])

      if origin_city.nil? || destination_city.nil?
        return {
          type: 'error',
          content: 'I couldn\'t find the cities you mentioned. Please provide valid city names with state (e.g., "Chicago, IL") or state names.'
        }
      end

      # Build lane search form with the found cities and optional filters
      form_params = {
        origin: origin_city,
        destination: destination_city
      }

      # Add optional filters if provided
      form_params[:truck_type] = lookup_truck_types(requirements['truck_type']) if requirements['truck_type'].present?
      form_params[:shipment_type] = lookup_shipment_types(requirements['shipment_type']) if requirements['shipment_type'].present?
      form_params[:specialized_service] = lookup_specialized_services(requirements['specialized_service']) if requirements['specialized_service'].present?
      form_params[:freight] = lookup_freights(requirements['freight']) if requirements['freight'].present?

      form = Forms::LaneSearch.new(form_params)

      {
        type: 'requirements',
        data: {
          origin: origin_city.label,
          destination: destination_city.label,
          volume: requirements['volume'],
          frequency: requirements['frequency'],
          **form_params.except(:origin, :destination)
        },
        elasticsearch_query: form.to_es_query
      }
    rescue JSON::ParserError
      { type: 'message', content: content }
    end

    def lookup_truck_types(names)
      return [] if names.blank?
      Array.wrap(names).filter_map { |name| find_record_by_name(Records[:truck_types].all, name) }
    end

    def lookup_shipment_types(names)
      return [] if names.blank?
      Array.wrap(names).filter_map { |name| find_record_by_name(Records[:shipment_types].all, name) }
    end

    def lookup_specialized_services(names)
      return [] if names.blank?
      Array.wrap(names).filter_map { |name| find_record_by_name(Records[:specialized_services].all, name) }
    end

    def lookup_freights(names)
      return [] if names.blank?
      Array.wrap(names).filter_map { |name| find_record_by_name(Records[:freights].all, name) }
    end

    def find_record_by_name(records, name)
      name_lower = name.downcase.strip

      # Try exact match first
      record = records.find { |r| r.name.downcase == name_lower }
      return record.id if record

      # Try partial match for common variations
      record = records.find { |r| r.name.downcase.include?(name_lower) || name_lower.include?(r.name.downcase) }
      return record.id if record

      # Try key-based matching for truck types
      if records.first.respond_to?(:key)
        record = records.find { |r| r.key.downcase == name_lower.gsub(/\s+/, '_') }
        return record.id if record
      end

      nil
    end
  end
end
