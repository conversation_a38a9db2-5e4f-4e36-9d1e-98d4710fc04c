# AI Chatbot API for Shipper-Carrier Prospecting

## Overview

The AI Chatbot API helps shippers find carriers by collecting shipping requirements through natural language conversation. The chatbot collects origin, destination, and optional shipping preferences, then returns structured data for Elasticsearch carrier searches.

## Endpoint

```
POST /api/ai_chatbot
```

## Request Format

```json
{
  "messages": [
    {
      "role": "user",
      "content": "I need to ship from Chicago to Los Angeles"
    },
    {
      "role": "assistant", 
      "content": "Great! What type of truck do you need?"
    },
    {
      "role": "user",
      "content": "Dry van"
    }
  ]
}
```

## Response Types

### 1. Conversational Message

When the chatbot needs more information:

```json
{
  "type": "message",
  "content": "What's your destination city?"
}
```

### 2. Requirements Collected

When enough information is gathered:

```json
{
  "type": "requirements",
  "data": {
    "origin": "Chicago, IL",
    "destination": "Los Angeles, CA",
    "truck_type": [1],
    "shipment_type": [2],
    "volume": "10 pallets",
    "frequency": "weekly"
  },
  "elasticsearch_query": {
    "filters": {
      "truck_type_ids": [1],
      "shipment_type_ids": [2]
    },
    "shoulds": {
      "preferred_lanes": {
        "radius": 50,
        "pickup": {"lat": 41.8375, "lon": -87.6866},
        "dropoff": {"lat": 34.0522, "lon": -118.2437}
      },
      "review_lanes": {
        "radius": 50,
        "pickup": {"lat": 41.8375, "lon": -87.6866},
        "dropoff": {"lat": 34.0522, "lon": -118.2437}
      },
      "operation_states": {
        "radius": 50,
        "states": [5],
        "lat": 41.8375,
        "lon": -87.6866
      }
    },
    "order": ["cs_score"]
  }
}
```

### 3. Error Response

When there's an issue:

```json
{
  "type": "error",
  "content": "I couldn't find the cities you mentioned. Please provide valid city names."
}
```

## Required Information

- **Origin**: City, state, or region in the US
- **Destination**: City, state, or region in the US

## Optional Information

- **Truck Type**: Dry van, reefer, flatbed, etc.
- **Shipment Type**: Full truckload, LTL, etc.
- **Specialized Services**: Drayage, expedited, etc.
- **Freights**: Automotive, food, chemicals, etc.
- **Volume**: Free-form description (e.g., "10 pallets", "5000 lbs")
- **Frequency**: Free-form description (e.g., "weekly", "monthly")

## Usage Example

```javascript
// Start conversation
const response1 = await fetch('/api/ai_chatbot', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    messages: [
      { role: 'user', content: 'I need to find carriers for shipping' }
    ]
  })
});

// Continue conversation
const response2 = await fetch('/api/ai_chatbot', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    messages: [
      { role: 'user', content: 'I need to find carriers for shipping' },
      { role: 'assistant', content: 'I\'d be happy to help! Where will you be shipping from?' },
      { role: 'user', content: 'Chicago, Illinois' }
    ]
  })
});

// When requirements are collected, use elasticsearch_query for carrier search
if (response.type === 'requirements') {
  const carrierSearch = await searchCarriers(response.elasticsearch_query);
}
```

## Error Handling

The API returns appropriate HTTP status codes:

- `200 OK`: Successful response
- `503 Service Unavailable`: OpenAI API issues
- `500 Internal Server Error`: Unexpected errors

## Integration with Existing Systems

The `elasticsearch_query` returned in the requirements response is compatible with the existing `Forms::LaneSearch` and can be used directly with the carrier search infrastructure.
