# CarrierNetworkBuilders AI Chatbot API

## Overview

The CarrierNetworkBuilders AI Chatbot API helps shippers find carriers and brokerages by collecting shipping requirements through natural language conversation. The chatbot uses smart inference to automatically determine truck types, shipment types, and specialized services based on freight descriptions, and returns locations in a hierarchical format for precise lookup. The structured data is then used for Elasticsearch searches to find both carriers and brokerages.

## Endpoint

```
POST /ai_chatbot
```

**Host:** www.carriersource.io (or www.lvh.me for local development)

## Request Format

```json
{
  "messages": [
    {
      "role": "user",
      "content": "I need to ship from Chicago to Los Angeles"
    },
    {
      "role": "assistant", 
      "content": "Great! What type of truck do you need?"
    },
    {
      "role": "user",
      "content": "Dry van"
    }
  ]
}
```

## Response Types

### 1. Conversational Message

When the chatbot needs more information:

```json
{
  "type": "message",
  "content": "What's your destination city?"
}
```

### 2. Requirements Collected

When enough information is gathered:

```json
{
  "type": "requirements",
  "data": {
    "origin": "Chicago, IL",
    "destination": "Los Angeles, CA",
    "truck_type": [1],
    "shipment_type": [2],
    "volume": "10 pallets",
    "frequency": "weekly"
  },
  "elasticsearch_query": {
    "filters": {
      "truck_type_ids": [1],
      "shipment_type_ids": [2]
    },
    "shoulds": {
      "preferred_lanes": {
        "radius": 50,
        "pickup": {"lat": 41.8375, "lon": -87.6866},
        "dropoff": {"lat": 34.0522, "lon": -118.2437}
      },
      "review_lanes": {
        "radius": 50,
        "pickup": {"lat": 41.8375, "lon": -87.6866},
        "dropoff": {"lat": 34.0522, "lon": -118.2437}
      },
      "operation_states": {
        "radius": 50,
        "states": [5],
        "lat": 41.8375,
        "lon": -87.6866
      }
    },
    "order": ["cs_score"]
  }
}
```

### 3. Error Response

When there's an issue:

```json
{
  "type": "error",
  "content": "I couldn't find the cities you mentioned. Please provide valid city names."
}
```

## Required Information

- **Origin**: City, state, or region (US, Canada, Mexico) in hierarchical format
- **Destination**: City, state, or region (US, Canada, Mexico) in hierarchical format

## Optional Information

- **Truck Type**: Returned as slugs (van, reefer, flatbed, etc.) - often auto-inferred from freight type
- **Shipment Type**: Returned as slugs (full-truckload, less-than-truckload, etc.) - often auto-inferred from volume/freight
- **Specialized Services**: Returned as slugs (hazardous-materials, team-drivers, etc.) - often auto-inferred from freight type
- **Freights**: Returned as slugs (coldfood, motoveh, chem, etc.)
- **Volume**: Free-form description (e.g., "10 pallets", "5000 lbs")
- **Frequency**: Free-form description (e.g., "weekly", "monthly")

## Smart Inference

The chatbot automatically infers appropriate truck types, shipment types, and specialized services based on freight descriptions, returning database slugs:

- **Refrigerated/frozen food** → `reefer` truck type, `coldfood` freight
- **Cars/vehicles** → `auto_carrier` truck type, `motoveh` freight
- **Chemicals** → `van` truck type, `chem` freight, `hazardous-materials` service (if hazmat)
- **Construction equipment** → `flatbed` truck type, `machlrg` freight
- **Small shipments** → `less-than-truckload` shipment type
- **Full trailer loads** → `full-truckload` shipment type
- **Urgent/expedited** → `team-drivers` specialized service

## Hierarchical Location Format

The chatbot returns locations in a structured hierarchical format for precise lookup:

### Format Examples:
- **Cities**: `"united-states:colorado:denver"`, `"canada:ontario:toronto"`, `"mexico:nuevo-leon:monterrey"`
- **States**: `"united-states:utah"`, `"canada:alberta"`, `"mexico:sonora"`
- **US Regions**: `"united-states:midwest"`, `"united-states:northeast"`, `"united-states:west"`

### Supported Regions:
- **US Only**: Northeast, Midwest, Southeast, Southwest, West
- **Canada/Mexico**: No regions (cities and states only)

### Benefits:
- **Precise Lookup**: Eliminates ambiguity between cities with same names
- **Consistent Format**: Standardized across all countries
- **Easy Parsing**: Simple colon-separated hierarchy

## Usage Example

```javascript
// Start conversation
const response1 = await fetch('https://www.carriersource.io/ai_chatbot', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    messages: [
      { role: 'user', content: 'I need to find carriers and brokerages for shipping' }
    ]
  })
});

// Continue conversation
const response2 = await fetch('https://www.carriersource.io/ai_chatbot', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    messages: [
      { role: 'user', content: 'I need to find carriers and brokerages for shipping' },
      { role: 'assistant', content: 'I\'d be happy to help! Where will you be shipping from?' },
      { role: 'user', content: 'Chicago, Illinois' }
    ]
  })
});

// When requirements are collected, use elasticsearch_query for carrier and brokerage search
if (response.type === 'requirements') {
  const results = await searchCarriersAndBrokerages(response.elasticsearch_query);
}
```

## Error Handling

The API returns appropriate HTTP status codes:

- `200 OK`: Successful response
- `503 Service Unavailable`: OpenAI API issues
- `500 Internal Server Error`: Unexpected errors

## Integration with Existing Systems

The `elasticsearch_query` returned in the requirements response is compatible with the existing `Forms::LaneSearch` and can be used directly with the carrier and brokerage search infrastructure.

## Implementation Details

The chatbot is implemented within the `CarrierNetworkBuilders` namespace:

- `CarrierNetworkBuilders::ShipperProspectingChatbot` - Main chatbot service
- `CarrierNetworkBuilders::LocationLookup` - Location parsing and validation
- Uses existing `Forms::LaneSearch` for Elasticsearch query generation
- Integrates with OpenAI Chat Completion API
