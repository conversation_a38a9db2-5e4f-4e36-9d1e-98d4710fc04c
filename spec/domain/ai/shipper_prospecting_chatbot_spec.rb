require 'rails_helper'

RSpec.describe AI::ShipperProspectingChatbot do
  subject(:chatbot) { described_class.new(messages) }

  let(:messages) { [] }
  let(:mock_client) { instance_double(OpenAI::Api::ChatCompletion) }
  let(:mock_response) { double('response', parse: response_data) }

  before do
    allow(OpenAI::Api::ChatCompletion).to receive(:new).and_return(mock_client)
    allow(mock_client).to receive(:create).and_return(mock_response)
  end

  describe '#call' do
    context 'when AI returns a conversational message' do
      let(:response_data) do
        {
          'choices' => [
            {
              'message' => {
                'content' => 'Hello! I\'d be happy to help you find carriers. Where will you be shipping from?'
              }
            }
          ]
        }
      end

      it 'returns a message response' do
        result = chatbot.call

        expect(result[:type]).to eq('message')
        expect(result[:content]).to include('Where will you be shipping from?')
      end
    end

    context 'when AI returns JSON requirements' do
      let(:chicago) { create(:city, name: 'Chicago', state_code: 'IL') }
      let(:los_angeles) { create(:city, name: 'Los Angeles', state_code: 'CA') }
      let(:response_data) do
        {
          'choices' => [
            {
              'message' => {
                'content' => {
                  origin: 'Chicago, IL',
                  destination: 'Los Angeles, CA',
                  truck_type: ['dry-van'],
                  volume: '10 pallets'
                }.to_json
              }
            }
          ]
        }
      end

      before do
        allow(AI::LocationLookup).to receive(:call).with('Chicago, IL').and_return(chicago)
        allow(AI::LocationLookup).to receive(:call).with('Los Angeles, CA').and_return(los_angeles)
      end

      it 'returns structured requirements' do
        result = chatbot.call

        expect(result[:type]).to eq('requirements')
        expect(result[:data][:origin]).to eq(chicago.label)
        expect(result[:data][:destination]).to eq(los_angeles.label)
        expect(result[:elasticsearch_query]).to have_key(:filters)
      end
    end

    context 'when AI returns incomplete JSON' do
      let(:response_data) do
        {
          'choices' => [
            {
              'message' => {
                'content' => {
                  origin: 'Chicago, IL'
                  # Missing destination
                }.to_json
              }
            }
          ]
        }
      end

      before do
        allow(AI::LocationLookup).to receive(:call).with('Chicago, IL').and_return(create(:city, name: 'Chicago', state_code: 'IL'))
        allow(AI::LocationLookup).to receive(:call).with(nil).and_return(nil)
      end

      it 'returns an error response' do
        result = chatbot.call

        expect(result[:type]).to eq('error')
        expect(result[:content]).to include('couldn\'t find the cities')
      end
    end

    context 'when OpenAI API fails' do
      before do
        allow(mock_client).to receive(:create).and_raise(OpenAI::Api::Error.new(double(status: 500, body: 'Error')))
      end

      it 'raises the error' do
        expect { chatbot.call }.to raise_error(OpenAI::Api::Error)
      end
    end

    context 'with conversation history' do
      let(:messages) do
        [
          { role: 'user', content: 'I need to ship from Chicago to LA' },
          { role: 'assistant', content: 'Great! What type of truck do you need?' },
          { role: 'user', content: 'Dry van' }
        ]
      end

      it 'includes conversation history in API call' do
        chatbot.call

        expect(mock_client).to have_received(:create) do |args|
          expect(args[:messages].length).to eq(4) # system + 3 conversation messages
          expect(args[:messages][0][:role]).to eq('system')
          expect(args[:messages][1][:role]).to eq('user')
          expect(args[:messages][1][:content]).to eq('I need to ship from Chicago to LA')
        end
      end
    end
  end
end
