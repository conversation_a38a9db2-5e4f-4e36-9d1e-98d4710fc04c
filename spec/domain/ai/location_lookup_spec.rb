require 'rails_helper'

RSpec.describe Ai::LocationLookup do
  subject(:lookup) { described_class.new(location_string, type) }

  let(:type) { nil }

  describe '#call' do
    context 'with blank location string' do
      let(:location_string) { '' }

      it 'returns nil' do
        expect(lookup.call).to be_nil
      end
    end

    context 'with city search' do
      let(:location_string) { 'Chicago, IL' }
      let(:type) { 'city' }
      let(:chicago) { create(:city, name: 'Chicago', state_code: 'IL') }

      before do
        allow(Cities::PrefixQuery).to receive(:call).and_return({})
        allow(City).to receive_message_chain(:es, :search, :page, :per, :results).and_return([
          double(_source: { 'id' => chicago.id })
        ])
        allow(City).to receive(:find).with(chicago.id).and_return(chicago)
      end

      it 'finds the city using elasticsearch' do
        result = lookup.call
        expect(result).to eq(chicago)
      end
    end

    context 'with city and state format' do
      let(:location_string) { 'Chicago, Illinois' }
      let(:illinois) { double('state', abbr: 'IL', name: 'Illinois') }
      let(:chicago) { create(:city, name: 'Chicago', state_code: 'IL') }

      before do
        allow(Cities::PrefixQuery).to receive(:call).and_return({})
        allow(City).to receive_message_chain(:es, :search, :page, :per, :results).and_return([])
        allow(Geo::State).to receive(:where).with('LOWER(name) = ?', 'illinois').and_return([illinois])
        allow(City).to receive(:where).with(state_code: illinois.abbr).and_return(
          double(where: [chicago])
        )
      end

      it 'parses city and state separately' do
        result = lookup.call
        expect(result).to eq(chicago)
      end
    end

    context 'with state search' do
      let(:location_string) { 'California' }
      let(:type) { 'state' }
      let(:california) { double('state', name: 'California', abbr: 'CA') }

      before do
        allow(Geo::State).to receive(:where).with('LOWER(name) = ?', 'california').and_return([california])
      end

      it 'finds the state by name' do
        result = lookup.call
        expect(result).to eq(california)
      end
    end

    context 'with state abbreviation' do
      let(:location_string) { 'CA' }
      let(:type) { 'state' }
      let(:california) { double('state', name: 'California', abbr: 'CA') }

      before do
        allow(Geo::State).to receive(:where).with('LOWER(name) = ?', 'ca').and_return([])
        allow(Geo::State).to receive(:where).with('LOWER(abbr) = ?', 'ca').and_return([california])
      end

      it 'finds the state by abbreviation' do
        result = lookup.call
        expect(result).to eq(california)
      end
    end

    context 'with region search' do
      let(:location_string) { 'West' }
      let(:type) { 'region' }
      let(:west_region) { double('region', name: 'West') }

      before do
        allow(Geo::Region).to receive(:where).with('LOWER(name) = ?', 'west').and_return([west_region])
      end

      it 'finds the region by name' do
        result = lookup.call
        expect(result).to eq(west_region)
      end
    end

    context 'without specific type' do
      let(:location_string) { 'Chicago' }
      let(:chicago) { create(:city, name: 'Chicago', state_code: 'IL') }

      before do
        allow(Cities::PrefixQuery).to receive(:call).and_return({})
        allow(City).to receive_message_chain(:es, :search, :page, :per, :results).and_return([
          double(_source: { 'id' => chicago.id })
        ])
        allow(City).to receive(:find).with(chicago.id).and_return(chicago)
      end

      it 'tries to find city first' do
        result = lookup.call
        expect(result).to eq(chicago)
      end
    end

    context 'when location is not found' do
      let(:location_string) { 'NonexistentPlace' }

      before do
        allow(Cities::PrefixQuery).to receive(:call).and_return({})
        allow(City).to receive_message_chain(:es, :search, :page, :per, :results).and_return([])
        allow(Geo::State).to receive(:where).and_return([])
        allow(Geo::Region).to receive(:where).and_return([])
      end

      it 'returns nil' do
        result = lookup.call
        expect(result).to be_nil
      end
    end
  end
end
