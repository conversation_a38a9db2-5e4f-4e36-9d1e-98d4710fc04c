require 'rails_helper'

RSpec.describe Ai::LocationLookup do
  subject(:lookup) { described_class.new(location_string, type) }

  let(:type) { nil }

  describe '#call' do
    context 'with blank location string' do
      let(:location_string) { '' }

      it 'returns nil' do
        expect(lookup.call).to be_nil
      end
    end

    context 'with city search' do
      let(:location_string) { 'Chicago, IL' }
      let(:type) { 'city' }
      let(:chicago) { create(:city, name: 'Chicago', state_code: 'IL') }

      before do
        allow(Cities::PrefixQuery).to receive(:call).and_return({})
        allow(City).to receive_message_chain(:es, :search, :page, :per, :results).and_return([
          double(_source: { 'id' => chicago.id })
        ])
        allow(City).to receive(:find).with(chicago.id).and_return(chicago)
      end

      it 'finds the city using elasticsearch' do
        result = lookup.call
        expect(result).to eq(chicago)
      end
    end

    context 'with city and state format' do
      let(:location_string) { 'Chicago, Illinois' }
      let(:illinois) { double('state', abbr: 'IL', name: 'Illinois') }
      let(:chicago) { create(:city, name: 'Chicago', state_code: 'IL') }
      let(:states_collection) { double('states_collection', find_by: illinois) }
      let(:us_country) { double('country', states: states_collection) }

      before do
        allow(Cities::PrefixQuery).to receive(:call).and_return({})
        allow(City).to receive_message_chain(:es, :search, :page, :per, :results).and_return([])
        allow(Geo::Country).to receive(:find).with('us').and_return(us_country)
        allow(Geo::Country).to receive(:find).with('US').and_return(us_country)
        allow(us_country.states).to receive(:find).and_return(illinois)
        allow(City).to receive(:where).with(state_code: illinois.abbr).and_return(
          double(where: [chicago])
        )
      end

      it 'parses city and state separately' do
        result = lookup.call
        expect(result).to eq(chicago)
      end
    end

    context 'with state search' do
      let(:location_string) { 'California' }
      let(:type) { 'state' }
      let(:california) { double('state', name: 'California', abbr: 'CA') }
      let(:us_country) { double('country', states: [california]) }

      before do
        allow(Geo::Country).to receive(:find).with('us').and_return(us_country)
      end

      it 'finds the state by name' do
        result = lookup.call
        expect(result).to eq(california)
      end
    end

    context 'with state abbreviation' do
      let(:location_string) { 'CA' }
      let(:type) { 'state' }
      let(:california) { double('state', name: 'California', abbr: 'CA') }
      let(:other_state) { double('state', name: 'Colorado', abbr: 'CO') }
      let(:us_country) { double('country', states: [other_state, california]) }

      before do
        allow(Geo::Country).to receive(:find).with('us').and_return(us_country)
      end

      it 'finds the state by abbreviation' do
        result = lookup.call
        expect(result).to eq(california)
      end
    end

    context 'with region search' do
      let(:location_string) { 'West' }
      let(:type) { 'region' }
      let(:west_region) { double('region', name: 'West') }
      let(:us_country) { double('country', regions: [west_region]) }

      before do
        allow(Geo::Country).to receive(:find).with('us').and_return(us_country)
      end

      it 'finds the region by name' do
        result = lookup.call
        expect(result).to eq(west_region)
      end
    end

    context 'without specific type' do
      let(:location_string) { 'Chicago' }
      let(:chicago) { create(:city, name: 'Chicago', state_code: 'IL') }

      before do
        allow(Cities::PrefixQuery).to receive(:call).and_return({})
        allow(City).to receive_message_chain(:es, :search, :page, :per, :results).and_return([
          double(_source: { 'id' => chicago.id })
        ])
        allow(City).to receive(:find).with(chicago.id).and_return(chicago)
      end

      it 'tries to find city first' do
        result = lookup.call
        expect(result).to eq(chicago)
      end
    end

    context 'when location is not found' do
      let(:location_string) { 'NonexistentPlace' }
      let(:us_country) { double('country', states: [], regions: []) }

      before do
        allow(Cities::PrefixQuery).to receive(:call).and_return({})
        allow(City).to receive_message_chain(:es, :search, :page, :per, :results).and_return([])
        allow(Geo::Country).to receive(:find).with('us').and_return(us_country)
      end

      it 'returns nil' do
        result = lookup.call
        expect(result).to be_nil
      end
    end
  end
end
