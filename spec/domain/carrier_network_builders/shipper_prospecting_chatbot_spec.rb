require 'rails_helper'

RSpec.describe CarrierNetworkBuilders::ShipperProspectingChatbot do
  subject(:chatbot) { described_class.new(messages) }

  let(:messages) { [] }
  let(:mock_client) { instance_double(OpenAI::Api::ChatCompletion) }
  let(:mock_response) { double('response', parse: response_data) }

  before do
    allow(OpenAI::Api::ChatCompletion).to receive(:new).and_return(mock_client)
    allow(mock_client).to receive(:create).and_return(mock_response)
  end

  describe '#call' do
    context 'when AI returns a conversational message' do
      let(:response_data) do
        {
          'choices' => [
            {
              'message' => {
                'content' => 'Hello! I\'d be happy to help you find carriers and brokerages. Where will you be shipping from?'
              }
            }
          ]
        }
      end

      it 'returns a message response' do
        result = chatbot.call

        expect(result[:type]).to eq('message')
        expect(result[:content]).to include('carriers and brokerages')
      end
    end

    context 'when AI returns JSON requirements with inferred truck types' do
      let(:chicago) { create(:city, name: 'Chicago', state_code: 'IL') }
      let(:los_angeles) { create(:city, name: 'Los Angeles', state_code: 'CA') }
      let(:response_data) do
        {
          'choices' => [
            {
              'message' => {
                'content' => {
                  origin: 'united-states:illinois:chicago',
                  destination: 'united-states:california:los-angeles',
                  truck_type: ['reefer'],
                  freight: ['coldfood'],
                  shipment_type: ['ftl'],
                  volume: '10 pallets'
                }.to_json
              }
            }
          ]
        }
      end

      before do
        # Create test cities for the hierarchical lookup
        create(:city, name: 'Chicago', state_code: 'IL')
        create(:city, name: 'Los Angeles', state_code: 'CA')

        # Mock the Geo::Country lookup
        us_country = double('us_country', states: [], regions: [])
        allow(Geo::Country).to receive(:find).with('us').and_return(us_country)
        allow(Geo::Country).to receive(:find).with('US').and_return(us_country)

        # Mock states
        illinois = double('illinois', abbr: 'IL', slug: 'illinois', name: 'Illinois', id: 1)
        california = double('california', abbr: 'CA', slug: 'california', name: 'California', id: 2)
        states_collection = double('states_collection', find_by: nil)
        allow(states_collection).to receive(:find_by).with(abbr: 'IL').and_return(illinois)
        allow(states_collection).to receive(:find_by).with(abbr: 'CA').and_return(california)
        allow(us_country).to receive(:states).and_return(states_collection)

        # Also mock the array-like behavior for our lookup
        allow(states_collection).to receive(:find).and_return(nil)
        allow(states_collection).to receive(:find) { |&block| [illinois, california].find(&block) }
      end

      it 'returns structured requirements with inferred types' do
        result = chatbot.call

        expect(result[:type]).to eq('requirements')
        expect(result[:data]).to have_key(:origin)
        expect(result[:data]).to have_key(:destination)
        expect(result[:data][:truck_type]).to include(truck_types(:reefer).id)
        expect(result[:data][:freight]).to include(freights(:coldfood).id)
        expect(result[:data][:shipment_type]).to include(shipment_types(:ftl).id)
        expect(result[:elasticsearch_query]).to have_key(:filters)
      end
    end

    context 'when AI returns incomplete JSON' do
      let(:response_data) do
        {
          'choices' => [
            {
              'message' => {
                'content' => {
                  origin: 'united-states:illinois:chicago'
                  # Missing destination
                }.to_json
              }
            }
          ]
        }
      end

      before do
        # No need to mock since we're testing the error case with incomplete JSON
      end

      it 'returns an error response' do
        result = chatbot.call

        expect(result[:type]).to eq('error')
        expect(result[:content]).to include('couldn\'t find the locations')
      end
    end

    context 'when OpenAI API fails' do
      let(:response_data) { {} } # Not used but needed for mock_response

      before do
        allow(mock_client).to receive(:create).and_raise(OpenAI::Api::Error.new(double(status: 500, body: 'Error')))
      end

      it 'raises the error' do
        expect { chatbot.call }.to raise_error(OpenAI::Api::Error)
      end
    end

    context 'with conversation history' do
      let(:messages) do
        [
          { role: 'user', content: 'I need to ship from Chicago to LA' },
          { role: 'assistant', content: 'Great! What type of truck do you need?' },
          { role: 'user', content: 'Dry van' }
        ]
      end
      let(:response_data) do
        {
          'choices' => [
            {
              'message' => {
                'content' => 'Perfect! Any other requirements?'
              }
            }
          ]
        }
      end

      it 'includes conversation history in API call' do
        chatbot.call

        expect(mock_client).to have_received(:create) do |args|
          expect(args[:messages].length).to eq(4) # system + 3 conversation messages
          expect(args[:messages][0][:role]).to eq('system')
          expect(args[:messages][1][:role]).to eq('user')
          expect(args[:messages][1][:content]).to eq('I need to ship from Chicago to LA')
        end
      end
    end
  end
end
