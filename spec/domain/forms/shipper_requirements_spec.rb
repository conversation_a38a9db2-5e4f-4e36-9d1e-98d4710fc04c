require 'rails_helper'

RSpec.describe Forms::ShipperRequirements do
  subject(:form) { described_class.new(attributes) }

  let(:base_attributes) do
    {
      origin_type: 'city',
      origin_value: 'Chicago, IL',
      destination_type: 'state',
      destination_value: 'California'
    }
  end

  describe '#valid?' do
    context 'with complete origin and destination' do
      let(:attributes) { base_attributes }

      it 'returns true' do
        expect(form.valid?).to be true
      end
    end

    context 'with missing origin' do
      let(:attributes) { base_attributes.except(:origin_type) }

      it 'returns false' do
        expect(form.valid?).to be false
      end
    end

    context 'with missing destination' do
      let(:attributes) { base_attributes.except(:destination_value) }

      it 'returns false' do
        expect(form.valid?).to be false
      end
    end
  end

  describe '#to_search_filters' do
    let(:attributes) do
      base_attributes.merge(
        truck_type: [truck_types(:van).id],
        shipment_type: [shipment_types(:ftl).id],
        volume: '10 pallets',
        frequency: 'weekly'
      )
    end

    it 'returns structured search filters' do
      filters = form.to_search_filters

      expect(filters[:origin]).to eq({ type: 'city', value: 'Chicago, IL' })
      expect(filters[:destination]).to eq({ type: 'state', value: 'California' })
      expect(filters[:truck_type_ids]).to eq([truck_types(:van).id])
      expect(filters[:shipment_type_ids]).to eq([shipment_types(:ftl).id])
      expect(filters[:volume]).to eq('10 pallets')
      expect(filters[:frequency]).to eq('weekly')
    end

    it 'excludes blank values' do
      form = described_class.new(base_attributes)
      filters = form.to_search_filters

      expect(filters).not_to have_key(:truck_type_ids)
      expect(filters).not_to have_key(:volume)
    end
  end

  describe '#to_es_query' do
    let(:attributes) do
      base_attributes.merge(
        truck_type: [truck_types(:van).id],
        volume: '10 pallets'
      )
    end

    it 'returns elasticsearch query without volume/frequency' do
      query = form.to_es_query

      expect(query[:filters][:origin]).to eq({ type: 'city', value: 'Chicago, IL' })
      expect(query[:filters][:truck_type_ids]).to eq([truck_types(:van).id])
      expect(query[:filters]).not_to have_key(:volume)
      expect(query[:filters]).not_to have_key(:frequency)
    end
  end
end
