require 'rails_helper'

RSpec.describe 'API AI Chatbot', type: :request do
  describe 'POST /ai_chatbot' do
    let(:messages) do
      [
        { role: 'user', content: 'I need to ship from Chicago to LA' }
      ]
    end
    let(:params) { { messages: messages } }

    context 'when chatbot returns a message' do
      let(:chatbot_response) do
        {
          type: 'message',
          content: 'Great! What type of truck do you need?'
        }
      end

      before do
        allow(CarrierNetworkBuilders::ShipperProspectingChatbot).to receive(:new).and_return(
          double(call: chatbot_response)
        )
      end

      it 'returns the chatbot response' do
        post '/ai_chatbot', params: params, headers: { 'Host' => 'www.lvh.me' }

        expect(response).to have_http_status(:ok)
        expect(JSON.parse(response.body)).to eq(chatbot_response.stringify_keys)
      end
    end

    context 'when chatbot returns requirements' do
      let(:chatbot_response) do
        {
          type: 'requirements',
          data: {
            origin: 'Chicago, IL',
            destination: 'Los Angeles, CA',
            truck_type: [1],
            volume: '10 pallets'
          },
          elasticsearch_query: {
            filters: { truck_type_ids: [1] },
            shoulds: {},
            order: [:cs_score]
          }
        }
      end

      before do
        allow(CarrierNetworkBuilders::ShipperProspectingChatbot).to receive(:new).and_return(
          double(call: chatbot_response)
        )
      end

      it 'returns the structured requirements' do
        post '/ai_chatbot', params: params, headers: { 'Host' => 'www.lvh.me' }

        expect(response).to have_http_status(:ok)
        parsed_response = JSON.parse(response.body)
        expect(parsed_response['type']).to eq('requirements')
        expect(parsed_response['data']['origin']).to eq('Chicago, IL')
        expect(parsed_response['elasticsearch_query']).to have_key('filters')
      end
    end

    context 'when OpenAI API fails' do
      before do
        chatbot_double = double('chatbot')
        allow(CarrierNetworkBuilders::ShipperProspectingChatbot).to receive(:new).and_return(chatbot_double)
        allow(chatbot_double).to receive(:call).and_raise(OpenAI::Api::Error.new(double(status: 500, body: 'Error')))
      end

      it 'returns a service unavailable error' do
        post '/ai_chatbot', params: params, headers: { 'Host' => 'www.lvh.me' }

        expect(response).to have_http_status(:service_unavailable)
        parsed_response = JSON.parse(response.body)
        expect(parsed_response['type']).to eq('error')
        expect(parsed_response['content']).to include('having trouble processing')
      end
    end

    context 'when an unexpected error occurs' do
      before do
        chatbot_double = double('chatbot')
        allow(CarrierNetworkBuilders::ShipperProspectingChatbot).to receive(:new).and_return(chatbot_double)
        allow(chatbot_double).to receive(:call).and_raise(StandardError, 'Unexpected error')
      end

      it 'returns an internal server error' do
        post '/ai_chatbot', params: params, headers: { 'Host' => 'www.lvh.me' }

        expect(response).to have_http_status(:internal_server_error)
        parsed_response = JSON.parse(response.body)
        expect(parsed_response['type']).to eq('error')
        expect(parsed_response['content']).to include('Something went wrong')
      end
    end

    context 'with empty messages' do
      let(:params) { { messages: [] } }
      let(:chatbot_response) do
        {
          type: 'message',
          content: 'Hello! I\'d be happy to help you find carriers. Where will you be shipping from?'
        }
      end

      before do
        allow(CarrierNetworkBuilders::ShipperProspectingChatbot).to receive(:new).with([]).and_return(
          double(call: chatbot_response)
        )
      end

      it 'handles empty conversation' do
        post '/ai_chatbot', params: params, headers: { 'Host' => 'www.lvh.me' }

        expect(response).to have_http_status(:ok)
        expect(JSON.parse(response.body)).to eq(chatbot_response.stringify_keys)
      end
    end

    context 'with invalid parameters' do
      let(:params) { { invalid: 'params' } }

      before do
        allow(CarrierNetworkBuilders::ShipperProspectingChatbot).to receive(:new).with([]).and_return(
          double(call: { type: 'message', content: 'Hello!' })
        )
      end

      it 'handles missing messages parameter' do
        post '/ai_chatbot', params: params, headers: { 'Host' => 'www.lvh.me' }

        expect(response).to have_http_status(:ok)
      end
    end
  end
end
